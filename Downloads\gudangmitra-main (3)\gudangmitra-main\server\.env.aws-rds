# AWS RDS Configuration Template
# Copy this file to .env and update with your actual AWS RDS details

# AWS RDS Database Configuration
DB_HOST=your-rds-endpoint.xxxxxxxxx.us-east-1.rds.amazonaws.com
DB_USER=admin
DB_PASSWORD=your-secure-password
DB_NAME=gudang1
DB_PORT=3306

# Server Configuration
PORT=3002
NODE_ENV=production

# CORS Configuration (update with your Netlify URL)
CORS_ORIGIN=https://your-app.netlify.app

# AWS RDS Migration Configuration (for migration script)
AWS_RDS_HOST=your-rds-endpoint.xxxxxxxxx.us-east-1.rds.amazonaws.com
AWS_RDS_USER=admin
AWS_RDS_PASSWORD=your-secure-password
AWS_RDS_DATABASE=gudang1
AWS_RDS_PORT=3306

# SSL Configuration (recommended for production)
DB_SSL=true
