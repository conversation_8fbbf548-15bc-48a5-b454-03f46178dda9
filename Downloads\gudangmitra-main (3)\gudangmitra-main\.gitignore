# Dependencies
node_modules/
server/node_modules/

# Build outputs
dist/
dist-ssr/
build/
server/dist/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables (keep .env.production for deployment)
.env
.env.local
.env.development.local
.env.test.local
server/.env.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov

# Temporary files
*.tmp
*.temp

# Database files
*.db
*.sqlite
*.sqlite3

# OS generated files
Thumbs.db
ehthumbs.db

# Application specific
migration-data/
exports/
uploads/
